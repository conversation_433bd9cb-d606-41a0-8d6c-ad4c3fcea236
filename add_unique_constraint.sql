-- This script adds a unique constraint to the reference_number column
-- in the submission table.
--
-- Before running this script, it is recommended to check for any existing
-- duplicate values in the reference_number column to avoid errors.
--
-- You can use the following query to find duplicate reference numbers:
--
-- SELECT reference_number, COUNT(*)
-- FROM submission
-- GROUP BY reference_number
-- HAVING COUNT(*) > 1;

ALTER TABLE `submission`
ADD CONSTRAINT `uk_submission_reference_number` UNIQUE (`reference_number`);