openapi: 3.0.0
info:
  title: Staggered Payment Service API
  description: API for managing staggered payments.
  version: 1.0.0
servers:
  - url: /api/staggered
paths:
  /bulk-validate:
    post:
      summary: Validate a bulk submission file
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /single-validate:
    post:
      summary: Validate a single payment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SingleValidRequestDTO"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /template-submission:
    get:
      summary: Get the template file for submissions
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
  /template-payment:
    get:
      summary: Get the template file for payments
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
  /bulk-submission:
    post:
      summary: Submit a bulk payment
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                mainFile:
                  type: string
                  format: binary
                supportingFiles:
                  type: array
                  items:
                    type: string
                    format: binary
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/GeneralBodyResponse"
  /single-submission:
    post:
      summary: Submit a single payment
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                data:
                  $ref: "#/components/schemas/SingleValidRequestDTO"
                supportingFiles:
                  type: array
                  items:
                    type: string
                    format: binary
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /submission/history:
    get:
      summary: Get submission history
      parameters:
        - in: query
          name: page
          schema:
            type: integer
          description: Page number
        - in: query
          name: size
          schema:
            type: integer
          description: Page size
        - in: query
          name: sort
          schema:
            type: string
          description: Sort by
        - in: query
          name: status
          schema:
            type: string
        - in: query
          name: startDate
          schema:
            type: string
        - in: query
          name: endDate
          schema:
            type: string
        - in: query
          name: currentUserOnly
          schema:
            type: boolean
        - in: query
          name: nip
          schema:
            type: string
        - in: query
          name: name
          schema:
            type: string
        - in: query
          name: paymentType
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /submission/history/{id}:
    get:
      summary: Get submission details
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /log-{taskname}:
    get:
      summary: Get transaction console log
      parameters:
        - in: path
          name: taskname
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /list-approval:
    get:
      summary: Get approval task list
      parameters:
        - in: query
          name: page
          schema:
            type: integer
          description: Page number
        - in: query
          name: size
          schema:
            type: integer
          description: Page size
        - in: query
          name: sort
          schema:
            type: string
          description: Sort by
        - in: query
          name: status
          schema:
            type: string
        - in: query
          name: startDate
          schema:
            type: string
        - in: query
          name: endDate
          schema:
            type: string
        - in: query
          name: nip
          schema:
            type: string
        - in: query
          name: name
          schema:
            type: string
        - in: query
          name: paymentType
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /download/{submissionId}/supporting/{fileId}:
    get:
      summary: Download a supporting file
      parameters:
        - in: path
          name: submissionId
          required: true
          schema:
            type: integer
            format: int64
        - in: path
          name: fileId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
  /bulk-payment:
    post:
      summary: Update payments in bulk
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /single-payment:
    post:
      summary: Update a single payment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentUpdateDTO"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /generate-payment:
    post:
      summary: Generate a payment Excel file
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GeneratePaymentRequestDTO"
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /action:
    post:
      summary: Process an action on tasks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BulkActionRequestDTO"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
  /report:
    post:
      summary: Generate a report
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ReportRequestDTO"
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneralBodyResponse"
components:
  schemas:
    SingleValidRequestDTO:
      type: object
      properties:
        nip:
          type: string
        name:
          type: string
        grade:
          type: string
        paymentType:
          type: string
        amount:
          type: number
        description:
          type: string
        monthOfProcess:
          type: string
        yearOfProcess:
          type: string
    GeneratePaymentRequestDTO:
      type: object
      properties:
        yearOfProcess:
          type: string
        monthOfProcess:
          type: string
    BulkActionRequestDTO:
      type: object
      properties:
        taskNames:
          type: array
          items:
            type: string
        action:
          type: string
        reason:
          type: string
        monthOfProcess:
          type: string
        yearOfProcess:
          type: string
        paymentType:
          type: string
        amount:
          type: number
    ReportRequestDTO:
      type: object
      properties:
        startDate:
          type: string
        endDate:
          type: string
        status:
          type: string
    PaymentUpdateDTO:
      type: object
      properties:
        referenceNumber:
          type: string
        paymentDate:
          type: string
          format: date
    GeneralBodyResponse:
      type: object
      properties:
        code:
          type: integer
        status:
          type: string
        message:
          type: string
        data:
          type: object
    SubmissionHistoryDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        taskName:
          type: string
        referenceNumber:
          type: string
        createdBy:
          type: string
        submitterName:
          type: string
        submitterJob:
          type: string
        status:
          type: string
        nip:
          type: string
        name:
          type: string
        grade:
          type: string
        paymentType:
          type: string
        amount:
          type: number
        description:
          type: string
        monthOfProcess:
          type: string
        yearOfProcess:
          type: string
        directorate:
          type: string
        slik:
          type: string
        sanction:
          type: string
        terminationDate:
          type: string
        eligible:
          type: boolean
        currentReviewer:
          type: string
        paymentDate:
          type: string
        remarks:
          type: string
    SubmissionDetailDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        taskName:
          type: string
        createdAt:
          type: string
          format: date-time
        referenceNumber:
          type: string
        createdBy:
          type: string
        submitterName:
          type: string
        submitterJob:
          type: string
        status:
          type: string
        nip:
          type: string
        name:
          type: string
        grade:
          type: string
        paymentType:
          type: string
        amount:
          type: number
        description:
          type: string
        monthOfProcess:
          type: string
        yearOfProcess:
          type: string
        directorate:
          type: string
        slik:
          type: string
        sanction:
          type: string
        terminationDate:
          type: string
        eligible:
          type: boolean
        currentReviewer:
          type: string
        paymentDate:
          type: string
        remarks:
          type: string
        supportingFiles:
          type: array
          items:
            $ref: "#/components/schemas/SupportingFileDTO"
    TaskListDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        taskName:
          type: string
        referenceNumber:
          type: string
        submitterName:
          type: string
        submitterJob:
          type: string
        status:
          type: string
        nip:
          type: string
        name:
          type: string
        grade:
          type: string
        paymentType:
          type: string
        amount:
          type: number
        description:
          type: string
        monthOfProcess:
          type: string
        yearOfProcess:
          type: string
        directorate:
          type: string
        slik:
          type: string
        sanction:
          type: string
        terminationDate:
          type: string
        eligible:
          type: boolean
        paymentDate:
          type: string
        remarks:
          type: string
    SupportingFileDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        fileName:
          type: string
        filePath:
          type: string
