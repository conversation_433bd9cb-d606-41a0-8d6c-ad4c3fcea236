package com.dwdo.hotdesk.model;

import lombok.*;
import com.dwdo.hotdesk.model.audit.DateAudit;
import javax.persistence.*;
import java.time.LocalDate;

@Entity
@Table(name = "submission_payment",
    indexes = {
        @Index(name="idx_submission_payment_task_name", columnList = "task_name")
    }
)
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmissionPayment extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_name")
    private String taskName;

    @Column(columnDefinition = "LONGTEXT", name = "current_reviewer")
    private String currentReviewer;

    @Column(name = "payment_date")
    private LocalDate paymentDate;

    @Column(name = "submission_id", nullable = false)
    private Long submissionId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "submission_id", referencedColumnName = "id", insertable = false, updatable = false)
    private Submission submission;
}
