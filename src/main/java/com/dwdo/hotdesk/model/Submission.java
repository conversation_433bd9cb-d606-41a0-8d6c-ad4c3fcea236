package com.dwdo.hotdesk.model;

import lombok.*;
import com.dwdo.hotdesk.model.audit.DateAudit;
import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.math.BigDecimal;

@Entity
@Table(name = "submission",
    indexes = {
        @Index(name="idx_submission_task_name", columnList = "task_name"),
        @Index(name="idx_submission_status", columnList = "status"),
        @Index(name="idx_submission_createdBy", columnList = "created_by"),
        @Index(name="idx_submission_nip", columnList = "nip"),
        @Index(name="idx_submission_name", columnList = "name")
    },
    uniqueConstraints = {
        @UniqueConstraint(name = "uk_submission_composite_key",
                         columnNames = {"nip", "month_of_process", "year_of_process", "payment_type"})
    }
)
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Submission extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "submitter_name", nullable=false)
    private String submitterName;

    @Column(name = "submitter_job", nullable = false)
    private String submitterJob;

    // Submission metadata
    @Column(name = "task_name", nullable=false)
    private String taskName;

    // Payment details
    @Column(nullable = false)
    private String nip;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    private String grade;

    @Column(name = "payment_type", nullable = false)
    private String paymentType;

    @Column(nullable = false)
    private BigDecimal amount;
    
    @Column(nullable = false)
    private String description;
    
    @Column(name = "month_of_process", nullable = false)
    private String monthOfProcess;
    
    @Column(name = "year_of_process", nullable = false)
    private String yearOfProcess;

    // Validate details
    @Column
    private String directorate;
    
    @Column
    private String slik;
    
    @Column
    private String sanction;

    @Column(name = "termination_date")
    private String terminationDate;
    
    @Column
    private Boolean eligible;

    @Column(name = "reference_number" ,nullable = false, unique = true)
    private String referenceNumber;

    @Column(columnDefinition = "LONGTEXT", name = "current_reviewer")
    private String currentReviewer;

    @Column(nullable = false)
    private String status;

    @Column(name = "remarks")
    private String remarks;

    @ManyToOne
    @JoinColumn(name = "main_file_id", nullable = true)
    private MainFile mainFile;

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "submission_supporting_files",
        joinColumns = @JoinColumn(name = "submission_id"),
        inverseJoinColumns = @JoinColumn(name = "supporting_files_id")
    )
    @Builder.Default
    private List<SupportingFiles> supportingFiles = new ArrayList<>();

    @OneToOne(mappedBy = "submission", cascade = CascadeType.ALL)
    private SubmissionPayment submissionPayment;
}
