package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SubmissionFilterService {

    private final PaymentSubmissionRepository submissionRepository;

    public LocalDateTime parseStartDate(String startDateStr) {
        if (startDateStr == null || startDateStr.isEmpty()) {
            return null;
        }
        
        try {
            LocalDateTime startDate = parseDateTime(startDateStr, true);
            log.info("[FilterService] Parsed start date: {}", startDate);
            return startDate;
        } catch (Exception e) {
            String errorMsg = "Invalid start date format: " + startDateStr;
            log.warn("[FilterService] {}", errorMsg);
            throw CustomBadRequestException.badRequest(errorMsg);
        }
    }

    public LocalDateTime parseEndDate(String endDateStr) {
        if (endDateStr == null || endDateStr.isEmpty()) {
            return null;
        }
        
        try {
            LocalDateTime endDate = parseDateTime(endDateStr, false);
            log.info("[FilterService] Parsed end date: {}", endDate);
            return endDate;
        } catch (Exception e) {
            String errorMsg = "Invalid end date format: " + endDateStr;
            log.warn("[FilterService] {}", errorMsg);
            throw CustomBadRequestException.badRequest(errorMsg);
        }
    }

    private LocalDateTime parseDateTime(String dateStr, boolean isStartDate) {
        try {
            return LocalDateTime.parse(dateStr);
        } catch (Exception e) {
            try {
                LocalDate date = LocalDate.parse(dateStr);
                return isStartDate ? date.atStartOfDay() : date.atTime(23, 59, 59);
            } catch (Exception ex) {
                throw new IllegalArgumentException("Invalid date format: " + dateStr, ex);
            }
        }
    }

    public void validateDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        if (startDate != null && endDate != null) {
            if (startDate.toLocalDate().isAfter(endDate.toLocalDate())) {
                String errorMsg = "Start date must be before or on the same day as end date. Start: " +
                        startDate.toLocalDate() + ", End: " + endDate.toLocalDate();
                log.warn("[FilterService] Date validation failed: {}", errorMsg);
                throw CustomBadRequestException.badRequest(errorMsg);
            }
            log.info("[FilterService] Date range validation passed: {} to {}",
                    startDate.toLocalDate(), endDate.toLocalDate());
        } else if (startDate != null) {
            log.info("[FilterService] Partial date filtering: from {} onwards", startDate.toLocalDate());
        } else if (endDate != null) {
            log.info("[FilterService] Partial date filtering: up to {}", endDate.toLocalDate());
        } else {
            log.info("[FilterService] No date filtering applied");
        }
    }

    public Page<Submission> fetchSubmissions(
            String userNIP,
            String status,
            LocalDateTime startDate,
            LocalDateTime endDate,
            String nip,
            String name,
            String paymentType,
            Pageable pageable) {

        validateDateRange(startDate, endDate);

        Specification<Submission> spec = buildSpecification(userNIP, status, startDate, endDate, nip, name, paymentType);

        Page<Submission> submissions = submissionRepository.findAll(spec, pageable);

        logQueryResults(userNIP, submissions.getTotalElements());

        return submissions;
    }

    private void logQueryResults(String userNIP, long totalElements) {
        if (userNIP != null && !userNIP.isEmpty()) {
            log.info("[FilterService] Found {} submissions for user {}", totalElements, userNIP);
        } else {
            log.info("[FilterService] Found {} submissions for all users", totalElements);
        }
    }

    private Specification<Submission> buildSpecification(
            String userNIP,
            String status,
            LocalDateTime startDate,
            LocalDateTime endDate,
            String nip,
            String name,
            String paymentType) {

        Specification<Submission> spec = Specification.where(null);

        spec = applyExactMatchFilters(spec, userNIP, status, paymentType);

        spec = applyDateRangeFilter(spec, startDate, endDate);

        spec = applyTextSearchFilters(spec, nip, name);

        return spec;
    }

    private Specification<Submission> applyExactMatchFilters(
            Specification<Submission> spec, String userNIP, String status, String paymentType) {

        if (userNIP != null && !userNIP.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("createdBy"), userNIP));
        }

        if (status != null && !status.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("status"), status));
        }

        if (paymentType != null && !paymentType.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("paymentType"), paymentType));
            log.info("[FilterService] Filtering submissions by payment type: {}", paymentType);
        }

        return spec;
    }

    private Specification<Submission> applyDateRangeFilter(
            Specification<Submission> spec, LocalDateTime startDate, LocalDateTime endDate) {

        if (startDate != null && endDate != null) {
            spec = spec.and((root, query, cb) -> cb.between(root.join("submissionPayment").get("paymentDate"),
                    startDate, endDate));
        } else if (startDate != null) {
            spec = spec.and((root, query, cb) -> cb.greaterThanOrEqualTo(
                    root.join("submissionPayment").get("paymentDate"), startDate));
            log.info("[FilterService] Filtering submissions from {} onwards", startDate.toLocalDate());
        } else if (endDate != null) {
            spec = spec.and((root, query, cb) -> cb.lessThanOrEqualTo(
                    root.join("submissionPayment").get("paymentDate"), endDate));
            log.info("[FilterService] Filtering submissions up to {}", endDate.toLocalDate());
        }

        return spec;
    }

    private Specification<Submission> applyTextSearchFilters(
            Specification<Submission> spec, String nip, String name) {

        if (nip != null && !nip.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("nip")),
                     nip.toLowerCase() + "%"));
        }

        if (name != null && !name.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("name")),
                     name.toLowerCase() + "%"));
        }
        
        return spec;
    }

    public Specification<Submission> buildFilterSpecification(
            String userNIP,
            String status,
            LocalDateTime startDate,
            LocalDateTime endDate,
            String nip,
            String name,
            String paymentType) {

        validateDateRange(startDate, endDate);

        return buildSpecification(userNIP, status, startDate, endDate, nip, name, paymentType);
    }

    public Pageable createPageable(int page, int size, String[] sort) {
        return PageRequest.of(page, size, Sort.by(createSortOrders(sort)));
    }

    public List<Sort.Order> createSortOrders(String[] sort) {
        List<Sort.Order> orders = new ArrayList<>();
        
        Sort.Direction direction = Sort.Direction.DESC;
        
        if (sort != null && sort.length > 0) {
            for (String sortParam : sort) {
                if (sortParam.equalsIgnoreCase("asc")) {
                    direction = Sort.Direction.ASC;
                    break;
                }
            }
        }
        
        // Always sort by id with the determined direction
        orders.add(new Sort.Order(direction, "id"));
        return orders;
    }
}
