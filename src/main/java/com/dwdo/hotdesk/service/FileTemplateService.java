package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileTemplateService {

    private static final String[] PAYMENT_TYPES = {
        "Bonus Staggered",
        "Retention Bonus",
        "Token",
        "Performance Staggered",
        "Salary Adjustment",
        "Promotion",
        "Retention Salary"
    };

    private static final String[] MONTHS = {
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    };

    private static final String[] GRADES = {
        "U1", "U2", "U3", "U4", "U5", "U6", "U7", "U8", "U9", "U10", "U11"
    };

    public ResponseEntity<Resource> generateStaggeredPaymentTemplate() {
        log.info("[TemplateService] Generating submission template file");

        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("Submission Template");

            Row headerRow = sheet.createRow(0);
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            String[] columns = {"NIP", "Name", "Grade", "Payment Type", "Amount (IDR)", "Description", "Month of Process", "Year of Process"};

            for (int i = 0; i < columns.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(columns[i]);
                cell.setCellStyle(headerStyle);
                sheet.autoSizeColumn(i);
            }

            XSSFDataValidationHelper validationHelper = new XSSFDataValidationHelper(sheet);

            DataValidationConstraint gradeConstraint = validationHelper.createExplicitListConstraint(GRADES);
            CellRangeAddressList gradeAddressList = new CellRangeAddressList(1, 1000, 2, 2);
            XSSFDataValidation gradeValidation = (XSSFDataValidation) validationHelper.createValidation(gradeConstraint, gradeAddressList);
            gradeValidation.setSuppressDropDownArrow(true);
            gradeValidation.setEmptyCellAllowed(true);
            gradeValidation.setShowPromptBox(false);
            gradeValidation.setShowErrorBox(false);
            sheet.addValidationData(gradeValidation);

            DataValidationConstraint paymentTypeConstraint = validationHelper.createExplicitListConstraint(PAYMENT_TYPES);
            CellRangeAddressList paymentTypeAddressList = new CellRangeAddressList(1, 1000, 3, 3);
            XSSFDataValidation paymentTypeValidation = (XSSFDataValidation) validationHelper.createValidation(paymentTypeConstraint, paymentTypeAddressList);
            paymentTypeValidation.setSuppressDropDownArrow(true);
            paymentTypeValidation.setEmptyCellAllowed(true);
            paymentTypeValidation.setShowPromptBox(false);
            paymentTypeValidation.setShowErrorBox(false);
            sheet.addValidationData(paymentTypeValidation);

            DataValidationConstraint monthConstraint = validationHelper.createExplicitListConstraint(MONTHS);
            CellRangeAddressList monthAddressList = new CellRangeAddressList(1, 1000, 6, 6);
            XSSFDataValidation monthValidation = (XSSFDataValidation) validationHelper.createValidation(monthConstraint, monthAddressList);
            monthValidation.setSuppressDropDownArrow(true);
            monthValidation.setEmptyCellAllowed(true);
            monthValidation.setShowPromptBox(false);
            monthValidation.setShowErrorBox(false);
            sheet.addValidationData(monthValidation);

            String[][] sampleData = {
                {"1234567", "Adi Surya", "U2", "Bonus Staggered", "15000000", "Q4 2024 Performance Bonus", "February", "2025"},
                {"2345678", "Budi Santoso", "U3", "Retention Bonus", "25000000", "Retention incentive for key talent", "March", "2025"},
                {"3456789", "Citra Dewi", "U1", "Token", "5000000", "Recognition token for outstanding work", "April", "2025"},
                {"4567890", "Doni Pratama", "U5", "Performance Staggered", "20000000", "Annual performance-based payment", "May", "2025"},
                {"5678901", "Eka Sari", "U1", "Salary Adjustment", "12000000", "Salary adjustment due to promotion", "June", "2025"}
            };

            for (int rowIndex = 0; rowIndex < sampleData.length; rowIndex++) {
                Row sampleRow = sheet.createRow(rowIndex + 1);
                String[] rowData = sampleData[rowIndex];

                for (int colIndex = 0; colIndex < 6; colIndex++) {
                    Cell cell = sampleRow.createCell(colIndex);
                    if (colIndex == 4) {
                        cell.setCellValue(Double.parseDouble(rowData[colIndex]));
                    } else {
                        cell.setCellValue(rowData[colIndex]);
                    }
                }

                Cell monthCell = sampleRow.createCell(6);
                monthCell.setCellValue(rowData[6]);

                Cell yearCell = sampleRow.createCell(7);
                yearCell.setCellValue(Integer.parseInt(rowData[7]));
            }

            for (int i = 0; i < columns.length; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray());

            log.info("[TemplateService] Template file generated successfully");

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=submission_template.xlsx")
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .contentLength(resource.contentLength())
                .body(resource);

        } catch (IOException e) {
            log.error("[TemplateService] Error generating template file", e);
            throw new CustomBadRequestException(500, "Internal Server Error",
                "Failed to generate template file due to IO error: " + e.getMessage());
        }
    }

    public ResponseEntity<Resource> generatePaymentTemplate() {
        log.info("[TemplateService] Generating payment template file");

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Template");

            Row headerRow = sheet.createRow(0);
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            String[] columns = {"Reference Number", "Payment Date"};

            for (int i = 0; i < columns.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(columns[i]);
                cell.setCellStyle(headerStyle);
                sheet.autoSizeColumn(i);
            }

            CellStyle dateStyle = workbook.createCellStyle();
            DataFormat df = workbook.createDataFormat();
            dateStyle.setDataFormat(df.getFormat("m/d/yy"));
            sheet.setDefaultColumnStyle(1, dateStyle);

            String[][] sampleData = {
                    {"SP-1234ABCD", "2025-06-10"},
                    {"SP-98824B5B", "2025-06-11"},
                    {"SP-1181D929", "2025-06-12"},
                    {"SP-3571F4D5", "2025-06-13"},
                    {"SP-39071440", "2025-06-14"}
            };

            for (int i = 0; i < sampleData.length; i++) {
                Row sampleRow = sheet.createRow(i + 1);
                sampleRow.createCell(0).setCellValue(sampleData[i][0]);

                Cell dateCell = sampleRow.createCell(1);
                LocalDate localDate = LocalDate.parse(sampleData[i][1]);
                Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                dateCell.setCellValue(date);
                dateCell.setCellStyle(dateStyle);
            }

            for (int i = 0; i < columns.length; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray());

            log.info("[TemplateService] Payment template file generated successfully");

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=payment_template.xlsx")
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .contentLength(resource.contentLength())
                .body(resource);

        } catch (IOException e) {
            log.error("[TemplateService] Error generating payment template file", e);
            throw new CustomBadRequestException(500, "Internal Server Error",
                "Failed to generate payment template file due to IO error: " + e.getMessage());
        }
    }
}
