package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.PaymentUpdateDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SubmissionPaymentRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.service.feign.EmployeeClient;
import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentUpdateService {

    private final PaymentSubmissionRepository submissionRepository;
    private final SubmissionPaymentRepository submissionPaymentRepository;
    private final ApprovalService approvalService;

    @Autowired
    private EmployeeClient employeeClient;

    @Autowired
    private PaymentUpdateService self;

    @Value("${rule.code.payment-update}")
    private String paymentUpdateRuleCode;

    @Value("${system.code.payment-update}")
    private String paymentUpdateSystemCode;

    @Value("${process.name.payment-updatet}")
    private String paymentUpdateProcessName;

    private static final String EXCEL_TYPE_XLS = "application/vnd.ms-excel";
    private static final String EXCEL_TYPE_XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final DateTimeFormatter PRIMARY_DATE_FORMATTER = DateTimeFormatter.ofPattern("dd-MMM-yyyy");
    private static final DateTimeFormatter ALTERNATE_DATE_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    private static final DateTimeFormatter SHORT_DATE_FORMATTER = DateTimeFormatter.ofPattern("M/d/yy");

    public GeneralBodyResponse updatePaymentFromFile(MultipartFile file) {
        if (!isExcelFile(file)) {
            throw new CustomBadRequestException(400, "Invalid File", "Only Excel files are allowed");
        }

        try {
            log.info("[PaymentUpdateService] Processing payment update file: {}", file.getOriginalFilename());
            List<PaymentUpdateDTO> updates = processExcelFile(file);
            return self.processPaymentUpdates(updates);
        } catch (IOException e) {
            log.error("[PaymentUpdateService] Error processing Excel file", e);
            throw new CustomBadRequestException(400, "Processing Error", "Failed to process the Excel file");
        }
    }


    public GeneralBodyResponse updateSinglePayment(PaymentUpdateDTO update) {
        log.info("[PaymentUpdateService] Processing single payment update for reference: {}", update.getReferenceNumber());
        List<PaymentUpdateDTO> singleUpdateList = Collections.singletonList(update);
        return self.processPaymentUpdates(singleUpdateList);
    }

    public GeneralBodyResponse initiatePaymentUpdateApproval(List<PaymentUpdateDTO> paymentUpdates) {
        log.info("[PaymentUpdateApproval] Redirecting to new payment update flow for {} records", paymentUpdates.size());
        return self.processPaymentUpdates(paymentUpdates);
    }

    private PaymentUpdateValidationResults validatePaymentUpdates(List<PaymentUpdateDTO> paymentUpdates) {
        PaymentUpdateValidationResults results = new PaymentUpdateValidationResults();

        for (PaymentUpdateDTO update : paymentUpdates) {
            if (update.getReferenceNumber() == null || update.getReferenceNumber().isEmpty()) {
                log.error("[PaymentUpdateService] Reference number is required but not provided");
                throw new CustomBadRequestException(400, "Reference Number Required",
                    "Reference number is required but not provided");
            }

            Optional<Submission> submissionOpt = submissionRepository.findByReferenceNumber(update.getReferenceNumber());
            if (submissionOpt.isEmpty()) {
                log.error("[PaymentUpdateService] Reference number not found: {}", update.getReferenceNumber());
                throw new CustomBadRequestException(400, "Reference Number Not Found",
                    "Submission with reference number '" + update.getReferenceNumber() + "' does not exist");
            }

            Submission submission = submissionOpt.get();

            if (!"Waiting for Payment".equals(submission.getStatus())) {
                String currentStatus = submission.getStatus();

                if ("Paid".equals(currentStatus)) {
                    log.error("[PaymentUpdateService] Reference number already paid: {}", update.getReferenceNumber());
                    throw new CustomBadRequestException(400, "Reference Number Already Paid",
                        "Reference number '" + update.getReferenceNumber() + "' has already been paid");
                } else if ("Unpaid".equals(currentStatus)) {
                    log.error("[PaymentUpdateService] Reference number already processed as unpaid: {}", update.getReferenceNumber());
                    throw new CustomBadRequestException(400, "Reference Number Already Processed",
                        "Reference number '" + update.getReferenceNumber() + "' has already been processed as unpaid");
                } else {
                    log.error("[PaymentUpdateService] Invalid status for reference {}: {}",
                        update.getReferenceNumber(), currentStatus);
                    throw new CustomBadRequestException(400, "Invalid Status",
                        "Submission with reference number '" + update.getReferenceNumber() +
                        "' has status '" + currentStatus + "', expected 'Waiting for Payment'");
                }
            }

            if (submission.getSubmissionPayment() != null) {
                log.error("[PaymentUpdateService] Payment record already exists for reference: {}", update.getReferenceNumber());
                throw new CustomBadRequestException(400, "Payment Record Already Exists",
                    "Payment record already exists for reference number '" + update.getReferenceNumber() +
                    "'. Cannot upload the same reference number twice.");
            }

            results.validUpdates.add(update);
            results.resultMap.put(update.getReferenceNumber(), "Valid for payment update");
        }

        return results;
    }

    private void insertPaymentRecords(List<PaymentUpdateDTO> validUpdates) {
        log.info("[PaymentUpdateService] Preparing to insert {} payment records", validUpdates.size());
    
        if (validUpdates.isEmpty()) {
            return;
        }

        List<SubmissionPayment> paymentRecords = new ArrayList<>();

        for (PaymentUpdateDTO update : validUpdates) {
            Optional<Submission> submissionOpt = submissionRepository.findByReferenceNumber(update.getReferenceNumber());
            
            if (submissionOpt.isPresent()) {
                Submission submission = submissionOpt.get();
                SubmissionPayment paymentRecord = SubmissionPayment.builder()
                        .submission(submission)
                        .paymentDate(update.getPaymentDate())
                        .build();
                paymentRecords.add(paymentRecord);
            } else {
                log.error("[PaymentUpdateService] A validated reference number was not found in the database: {}", update.getReferenceNumber());
                throw new CustomBadRequestException(500, "Inconsistent Data",
                        "A validated reference number was not found: " + update.getReferenceNumber());
            }
        }
    
        try {
            submissionPaymentRepository.saveAll(paymentRecords);
            log.info("[PaymentUpdateService] Successfully saved all {} payment records", paymentRecords.size());
        } catch (Exception e) {
            log.error("[PaymentUpdateService] Error during bulk save of payment records", e);
            throw new CustomBadRequestException(500, "Database Error", "Failed to save payment records.");
        }
    }

    private String submitPaymentUpdateToApproval(String submitterNIP, PaymentUpdateDTO paymentUpdate) {
        log.info("[PaymentUpdateApproval] Submitting individual payment update to approval - Submitter: {}, Reference: {}",
                submitterNIP, paymentUpdate.getReferenceNumber());

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("nip", submitterNIP);

        String submitterName = getSubmitterName(submitterNIP);

        return approvalService.submitToApprovalWithConfig(submitterNIP, submitterName,
                paymentUpdateRuleCode, paymentUpdateSystemCode, paymentUpdateProcessName, parameters);
    }

    private String getSubmitterName(String submitterNIP) {
        try {
            EpiccEmployeeProfileImpl employeeDetails = getEmployeeDetails(submitterNIP);
            String submitterName = employeeDetails.getLastName();
            log.info("[PaymentUpdateService] Retrieved submitter name: {} for NIP: {}", submitterName, submitterNIP);
            return submitterName;
        } catch (Exception e) {
            log.warn("[PaymentUpdateService] Failed to get employee details for NIP: {}, using NIP as name", submitterNIP, e);
            return submitterNIP;
        }
    }

    private EpiccEmployeeProfileImpl getEmployeeDetails(String nip) {
        ResponseEntity<EpiccEmployeeProfileImpl> response = employeeClient.getProfile(nip);
        if (response == null || response.getBody() == null) {
            throw new CustomBadRequestException(404, "Employee Not Found",
                    "Could not retrieve employee details for NIP: " + nip);
        }
        return response.getBody();
    }

    private void updatePaymentRecordWithTaskName(PaymentUpdateDTO paymentUpdate, String taskName) {
        log.info("[PaymentUpdateService] Updating payment record with taskName for reference: {}, taskName: {}",
                paymentUpdate.getReferenceNumber(), taskName);

        try {
            Optional<Submission> submissionOpt = submissionRepository.findByReferenceNumber(paymentUpdate.getReferenceNumber());
            if (submissionOpt.isPresent()) {
                Submission submission = submissionOpt.get();
                SubmissionPayment paymentRecord = submission.getSubmissionPayment();
                if (paymentRecord != null) {
                    paymentRecord.setTaskName(taskName);
                    submissionPaymentRepository.save(paymentRecord);
                    log.info("[PaymentUpdateService] Updated payment record with taskName for reference: {}",
                        paymentUpdate.getReferenceNumber());
                } else {
                    log.warn("[PaymentUpdateService] Payment record not found for reference: {}", paymentUpdate.getReferenceNumber());
                }

                submission.setStatus("Waiting for Payment Approval");
                submissionRepository.save(submission);
                log.info("[PaymentUpdateService] Updated submission status to 'Waiting for Payment Approval' for reference: {}",
                    paymentUpdate.getReferenceNumber());

                self.updateCurrentReviewerAsync(paymentUpdate.getReferenceNumber(), taskName);
            } else {
                log.warn("[PaymentUpdateService] Submission not found for reference: {}", paymentUpdate.getReferenceNumber());
            }
        } catch (Exception e) {
            log.error("[PaymentUpdateService] Error updating approval info for reference: {}",
                paymentUpdate.getReferenceNumber(), e);
            throw new CustomBadRequestException(500, "Database Error",
                    "Failed to update approval info for reference: " + paymentUpdate.getReferenceNumber());
        }
    }

    private void processIndividualPaymentUpdate(PaymentUpdateDTO paymentUpdate, String currentUserNip,
                                              List<String> taskNames, List<String> failedReferences) {
        try {
            String taskName = submitPaymentUpdateToApproval(currentUserNip, paymentUpdate);

            if (taskName != null) {
                updatePaymentRecordWithTaskName(paymentUpdate, taskName);
                taskNames.add(taskName);
                log.info("[PaymentUpdateService] Successfully processed payment update for reference: {} with taskName: {}",
                    paymentUpdate.getReferenceNumber(), taskName);
            } else {
                failedReferences.add(paymentUpdate.getReferenceNumber());
                log.error("[PaymentUpdateService] Failed to get taskName for reference: {}", paymentUpdate.getReferenceNumber());
            }
        } catch (Exception e) {
            failedReferences.add(paymentUpdate.getReferenceNumber());
            log.error("[PaymentUpdateService] Error processing payment update for reference: {}",
                paymentUpdate.getReferenceNumber(), e);
        }
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void updateCurrentReviewerAsync(String referenceNumber, String taskName) {
        log.info("[PaymentUpdateService] ASYNC: Updating current reviewer for reference: {}, taskName: {}",
                referenceNumber, taskName);

        try {
            Optional<Submission> submissionOpt = submissionRepository.findByReferenceNumber(referenceNumber);
            if (submissionOpt.isPresent()) {
                SubmissionPayment paymentRecord = submissionOpt.get().getSubmissionPayment();
                if (paymentRecord != null) {
                    approvalService.updateCurrentReviewerForTask(taskName, "REF:" + referenceNumber,
                            paymentRecord::setCurrentReviewer);

                    submissionPaymentRepository.save(paymentRecord);
                    log.info("[PaymentUpdateService] ASYNC: Successfully updated current reviewer for reference: {}",
                        referenceNumber);
                } else {
                    log.warn("[PaymentUpdateService] ASYNC: Payment record not found for reference: {}", referenceNumber);
                }
            } else {
                log.warn("[PaymentUpdateService] ASYNC: Submission not found for reference: {}", referenceNumber);
            }
        } catch (Exception e) {
            log.error("[PaymentUpdateService] ASYNC: Error updating current reviewer for reference: {}",
                referenceNumber, e);
        }
    }



    private static class PaymentUpdateValidationResults {
        List<PaymentUpdateDTO> validUpdates = new ArrayList<>();
        Map<String, String> resultMap = new HashMap<>();
        int invalidCount = 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public GeneralBodyResponse processPaymentUpdates(List<PaymentUpdateDTO> updates) {
        if (updates == null || updates.isEmpty()) {
            return GeneralBodyResponse.builder()
                    .code(400)
                    .status("Error")
                    .message("No payment updates provided")
                    .build();
        }

        String currentUserNip = SecurityUtil.getCurrentUserLogin()
                .orElseThrow(() -> new CustomBadRequestException(401, "Authentication Error",
                        "User not authenticated or NIP not available"));

        log.info("[PaymentUpdateService] Processing payment updates for {} records by user: {}",
                updates.size(), currentUserNip);

        try {
            PaymentUpdateValidationResults validationResults = validatePaymentUpdates(updates);

            if (validationResults.validUpdates.isEmpty()) {
                return GeneralBodyResponse.builder()
                        .code(400)
                        .status("Error")
                        .message("No valid payment updates found")
                        .data(validationResults.resultMap)
                        .build();
            }

            insertPaymentRecords(validationResults.validUpdates);

            List<String> taskNames = new ArrayList<>();
            List<String> failedReferences = new ArrayList<>();

            for (PaymentUpdateDTO paymentUpdate : validationResults.validUpdates) {
                processIndividualPaymentUpdate(paymentUpdate, currentUserNip, taskNames, failedReferences);
            }

            if (!failedReferences.isEmpty()) {
                log.error("[PaymentUpdateService] Some payment updates failed: {}", failedReferences);
                throw new CustomBadRequestException(500, "Processing Error",
                        "Failed to process some payment updates: " + failedReferences);
            }

            int successCount = taskNames.size();

            String message = String.format("Payment update processed successfully for %d records. %d invalid records.",
                    successCount, validationResults.invalidCount);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("taskNames", taskNames);
            responseData.put("successCount", successCount);
            responseData.put("invalidCount", validationResults.invalidCount);
            responseData.put("details", validationResults.resultMap);

            return GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message(message)
                    .data(responseData)
                    .build();

        } catch (Exception e) {
            log.error("[PaymentUpdateService] Error processing payment updates", e);
            return GeneralBodyResponse.builder()
                    .code(500)
                    .status("Error")
                    .message("Internal error occurred while processing payment updates: " + e.getMessage())
                    .build();
        }
    }



    private boolean isExcelFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType != null && (
                contentType.equals(EXCEL_TYPE_XLS) ||
                contentType.equals(EXCEL_TYPE_XLSX)
        );
    }

    private List<PaymentUpdateDTO> processExcelFile(MultipartFile file) throws IOException {
        List<PaymentUpdateDTO> updates = new ArrayList<>();
        
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null && isValidRow(row, i)) {
                    updates.add(createPaymentUpdateFromRow(row, i));
                }
            }
        }
        
        return updates;
    }

    private boolean isValidRow(Row row, int rowIndex) {
        String referenceNumber = getStringCellValue(row.getCell(0));
        
        if (referenceNumber == null || referenceNumber.isEmpty()) {
            log.warn("[PaymentUpdateService] Skipping row {} - missing reference number", rowIndex + 1);
            return false;
        }
        
        return true;
    }

    private PaymentUpdateDTO createPaymentUpdateFromRow(Row row, int rowIndex) {
        String referenceNumber = getStringCellValue(row.getCell(0));
        String paymentDateStr = getStringCellValue(row.getCell(1));
        
        LocalDate paymentDate = null;
        if (paymentDateStr != null && !paymentDateStr.isEmpty()) {
            try {
                paymentDate = parsePaymentDate(paymentDateStr);
            } catch (CustomBadRequestException e) {
                log.error("[PaymentUpdateService] Invalid date format in row {}: {}. Error: {}",
                    rowIndex + 1, paymentDateStr, e.getMessage());
                throw new CustomBadRequestException(400, "Invalid Date",
                        "Invalid date format in row " + (rowIndex + 1) + ": '" + paymentDateStr +
                        "'. Supported formats: dd-MMM-yyyy, dd-MM-yyyy, M/d/yy, yyyy-MM-dd, or dd-MMM-yyyy");
            }
        }
        
        return new PaymentUpdateDTO(referenceNumber, paymentDate);
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) return null;

        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> {
                if (DateUtil.isCellDateFormatted(cell)) {
                    DataFormatter formatter = new DataFormatter();
                    yield formatter.formatCellValue(cell);
                } else {
                    yield String.valueOf((long) cell.getNumericCellValue());
                }
            }
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            default -> null;
        };
    }

    private LocalDate parsePaymentDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }

        List<DateTimeFormatter> formatters = Arrays.asList(
                PRIMARY_DATE_FORMATTER,
                ALTERNATE_DATE_FORMATTER,
                SHORT_DATE_FORMATTER,
                DateTimeFormatter.ofPattern("dd-MMM-yyyy")
        );

        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDate.parse(dateStr, formatter);
            } catch (DateTimeParseException e) {
                log.debug("[PaymentUpdateService] Failed to parse date '{}' with format: {}", dateStr, formatter);
            }
        }
        throw new CustomBadRequestException(400, "Invalid Date",
                "Invalid date format: '" + dateStr + "'. Supported formats: dd-MMM-yyyy, dd-MM-yyyy, M/d/yy, yyyy-MM-dd, dd-MMM-yyyy");
    }
}
