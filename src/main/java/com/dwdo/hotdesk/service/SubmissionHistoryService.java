package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.SubmissionHistoryDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.service.feign.ApprovalClient;
import com.dwdo.hotdesk.util.NullUtil;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import com.dwdo.hotdesk.service.feign.response.DetailResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

@Slf4j
@Service
@RequiredArgsConstructor
public class SubmissionHistoryService {

    private final PaymentSubmissionRepository submissionRepository;
    private final SubmissionFilterService filterService;

    @Autowired
    private ApprovalClient approvalClient;

    @Transactional
    public Page<SubmissionHistoryDTO> getSubmissionHistoryWithApprovalDetails(
            Pageable pageable,
            String status,
            String startDateStr,
            String endDateStr,
            boolean currentUserOnly,
            String nip,
            String name,
            String paymentType) {

        try {
            log.info("[HistoryService] Retrieving submission history: page={}, size={}, sort={}, status={}, dateRange={} to {}, currentUserOnly={}, nip={}, name={}, paymentType={}",
                    pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort(), status, startDateStr, endDateStr, currentUserOnly, nip, name, paymentType);

            LocalDateTime startDate = filterService.parseStartDate(startDateStr);
            LocalDateTime endDate = filterService.parseEndDate(endDateStr);

            List<String> userRoles = SecurityUtil.getCurrentUserRole();
            log.info("[HistoryService] Current user roles: {}", userRoles);

            String userNIP = currentUserOnly ? SecurityUtil.getCurrentUserLogin().orElse(null) : null;
            if (userNIP != null) {
                log.info("[HistoryService] Filtering by current user: {}", userNIP);
            }

            Specification<Submission> spec = filterService.buildFilterSpecification(
                    userNIP, status, startDate, endDate, nip, name, paymentType);

            if (!currentUserOnly) {
                spec = applyRoleBasedFilter(spec, userRoles);
            }

            Page<Submission> submissions = submissionRepository.findAll(spec, pageable);
            log.info("[HistoryService] Found {} submissions based on filtering", submissions.getTotalElements());

            updateSubmissionsWithApprovalDetails(submissions);

            return submissions.map(this::mapToDTO);
        } catch (CustomBadRequestException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = "Error retrieving submission history: " + e.getMessage();
            log.error(errorMsg, e);
            throw new CustomBadRequestException(500, "Internal Server Error", errorMsg);
        }
    }

    private Specification<Submission> applyRoleBasedFilter(Specification<Submission> spec, List<String> userRoles) {

        if (userRoles.contains("ROLE_ADMIN_HRPAYROLL") || userRoles.contains("ROLE_ADMIN_HRPAYROLL_HEAD")) {
            log.info("[HistoryService] User has HRPAYROLL or HRPAYROLL_HEAD role, showing all submissions");
            return spec;
        }

        if (userRoles.contains("ROLE_ADMIN_HRBP")) {
            log.info("[HistoryService] User has HRBP role, filtering for HRBP submissions");
            return spec.and((root, query, cb) -> cb.equal(root.get("submitterJob"), "HRBP"));
        }

        if (userRoles.contains("ROLE_ADMIN_HRREWARD")) {
            log.info("[HistoryService] User has HRREWARD role, filtering for HRREWARD submissions");
            return spec.and((root, query, cb) -> cb.equal(root.get("submitterJob"), "HRREWARD"));
        }

        log.warn("[HistoryService] User has no relevant HR role, showing no results");
        return spec.and((root, query, cb) -> cb.equal(cb.literal(1), 0));
    }

    /**
     * Update a single submission with approval details
     */
    public void updateSubmissionWithApprovalDetails(Submission submission) {
        try {
            ApiResponse response = approvalClient.detail(submission.getTaskName());

            if (response == null || !response.isSuccess() || response.getData() == null) {
                String errorMsg = "Error getting approval details for submission ID " + submission.getId() + ": " +
                        (response != null ? response.getMessage() : "Null response");
                log.error(errorMsg);
                submission.setCurrentReviewer("");
                throw new CustomBadRequestException(500, "Approval Service Error", errorMsg);
            }

            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());
            DetailResponse detailResponse = mapper.convertValue(response.getData(), DetailResponse.class);

            if (detailResponse == null) {
                submission.setCurrentReviewer("");
                return;
            }

            submission.setCurrentReviewer("");
            extractAndSetApproverInfo(submission, detailResponse, mapper);

        } catch (Exception e) {
            String errorMsg = "Error updating submission with approval details for ID " + submission.getId() + ": " + e.getMessage();
            log.error(errorMsg);
            submission.setCurrentReviewer("");
            throw new CustomBadRequestException(500, "Approval Service Error", errorMsg);
        }
    }

    private SubmissionHistoryDTO mapToDTO(Submission submission) {
        String paymentDateDisplay = getPaymentDateDisplay(submission);

        return SubmissionHistoryDTO.builder()
                .id(submission.getId())
                .taskName(submission.getTaskName())
                .referenceNumber(submission.getReferenceNumber())
                .createdBy(submission.getCreatedBy())
                .submitterName(submission.getSubmitterName())
                .submitterJob(submission.getSubmitterJob())
                .status(submission.getStatus())
                .nip(submission.getNip())
                .name(submission.getName())
                .grade(submission.getGrade())
                .paymentType(submission.getPaymentType())
                .amount(submission.getAmount())
                .description(submission.getDescription())
                .monthOfProcess(submission.getMonthOfProcess())
                .yearOfProcess(submission.getYearOfProcess())
                .directorate(submission.getDirectorate())
                .slik(submission.getSlik())
                .sanction(submission.getSanction())
                .terminationDate(submission.getTerminationDate())
                .eligible(submission.getEligible())
                .currentReviewer(submission.getCurrentReviewer())
                .paymentDate(paymentDateDisplay)
                .remarks(NullUtil.toDisplayString(submission.getRemarks()))
                .build();
    }

    private String getPaymentDateDisplay(Submission submission) {
        SubmissionPayment payment = submission.getSubmissionPayment();
        if (payment != null && payment.getPaymentDate() != null) {
            return NullUtil.toDisplayString(payment.getPaymentDate());
        }
        return "-";
    }

    private void updateSubmissionsWithApprovalDetails(Page<Submission> submissions) {
        List<Submission> updatedSubmissions = new ArrayList<>();

        for (Submission submission : submissions) {
            try {
                updateSubmissionWithApprovalDetails(submission);
                updatedSubmissions.add(submission);
            } catch (Exception e) {
                String errorMsg = "Error updating submission " + submission.getId() + ": " + e.getMessage();
                log.error(errorMsg);
            }
        }

        if (!updatedSubmissions.isEmpty()) {
            submissionRepository.saveAll(updatedSubmissions);
        }
    }

    private void extractAndSetApproverInfo(Submission submission, DetailResponse detailResponse, ObjectMapper mapper) {
        if (detailResponse.getCurrentLayerState() == null) {
            return;
        }

        try {
            List<Map<String, Object>> approvers = (List<Map<String, Object>>) detailResponse.getCurrentLayerState().get("approvers");

            if (approvers == null || approvers.isEmpty()) {
                return;
            }

            List<Map<String, Object>> simplifiedApprovers = new ArrayList<>();

            for (Map<String, Object> approver : approvers) {
                Map<String, Object> simplifiedApprover = new HashMap<>();

                simplifiedApprover.put("approverCode", approver.get("approverCode"));
                simplifiedApprover.put("approverNik", approver.get("approverNik"));
                simplifiedApprover.put("approverName", approver.get("approverName"));
                simplifiedApprover.put("approverJob", approver.get("approverJob"));

                simplifiedApprovers.add(simplifiedApprover);
            }

            String approversJson = mapper.writeValueAsString(simplifiedApprovers);
            submission.setCurrentReviewer(approversJson);

            String currentApproverCode = (String) detailResponse.getCurrentLayerState().get("code");

            log.info("Updated current reviewer with simplified JSON data for {} approvers, Code={}",
                    approvers.size(), currentApproverCode);

        } catch (Exception e) {
            log.warn("Error extracting reviewer info from currentLayerState: {}", e.getMessage());
        }
    }
}
